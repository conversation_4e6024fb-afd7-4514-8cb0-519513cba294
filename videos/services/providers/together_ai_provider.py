"""
Together.AI Provider for image generation
Handles image_generation stage using Together.AI API
"""
import logging
import requests
import uuid
from typing import Dict, Any, Optional
from django.conf import settings

from .base import BaseServiceProvider, ProviderResponse, ProcessingStatus, ProviderError, CallbackMixin

logger = logging.getLogger(__name__)


class TogetherAIProvider(BaseServiceProvider, CallbackMixin):
    """
    Provider for Together.AI image generation API
    Supports AI-powered image generation for videos
    """
    
    # Stages supported by Together.AI
    SUPPORTED_STAGES = [
        'image_generation'
    ]
    
    # Together.AI API settings
    API_ENDPOINT = getattr(settings, 'TOGETHER_AI_API_ENDPOINT', '')
    
    def __init__(self):
        super().__init__('together_ai')
        self.api_key = getattr(settings, 'TOGETHER_AI_API_KEY', '')
        self.timeout = getattr(settings, 'TOGETHER_AI_REQUEST_TIMEOUT', 120)
        self.default_model = getattr(settings, 'TOGETHER_AI_DEFAULT_MODEL', 'runwayml/stable-diffusion-v1-5')
        self.max_retries = getattr(settings, 'TOGETHER_AI_MAX_RETRIES', 3)
    
    def get_supported_stages(self) -> list:
        """Get list of stages this provider supports"""
        return self.SUPPORTED_STAGES.copy()
    
    def process_stage(
        self, 
        video, 
        stage: str, 
        correlation_id: str, 
        payload: Optional[Dict[str, Any]] = None
    ) -> ProviderResponse:
        """
        Process image generation using Together.AI API
        
        Args:
            video: Video model instance
            stage: Stage name (should be 'image_generation')
            correlation_id: Correlation ID for tracking
            payload: Optional additional payload data
            
        Returns:
            ProviderResponse: Response with generated images
        """
        try:
            self.log_operation('process_stage', video.id, stage, {
                'correlation_id': correlation_id
            })
            
            # Validate stage
            if not self.validate_stage(stage):
                raise ProviderError(
                    f"Stage '{stage}' not supported by Together.AI provider",
                    self.provider_name,
                    stage,
                    'UNSUPPORTED_STAGE'
                )
            
            # Check API key
            if not self.api_key:
                raise ProviderError(
                    "Together.AI API key not configured",
                    self.provider_name,
                    stage,
                    'MISSING_API_KEY'
                )
            
            # Get image prompts from video's media generations
            image_prompts = self._get_image_prompts(video)
            if not image_prompts:
                raise ProviderError(
                    "No image prompts found for video",
                    self.provider_name,
                    stage,
                    'MISSING_IMAGE_PROMPTS'
                )
            
            # Generate images for each prompt
            generated_images = []
            
            for i, prompt in enumerate(image_prompts):
                try:
                    logger.info(f"Generating image {i+1}/{len(image_prompts)} for video {video.id}")
                    
                    # Prepare API payload
                    api_payload = self.prepare_payload(video, stage)
                    api_payload['prompt'] = prompt
                    
                    # Make API request
                    response_data = self._make_api_request(api_payload)
                    
                    # Process response
                    image_data = self._process_image_response(response_data, prompt, i)
                    generated_images.append(image_data)
                    
                except Exception as e:
                    logger.error(f"Failed to generate image {i+1}: {str(e)}")
                    # Continue with other images, but log the error
                    generated_images.append({
                        'error': str(e),
                        'prompt': prompt,
                        'index': i
                    })
            
            # Create MediaAssets for generated images
            self._create_media_assets(video, generated_images)
            
            return ProviderResponse.success(
                data={
                    'generated_images': generated_images,
                    'total_images': len(generated_images),
                    'successful_images': len([img for img in generated_images if 'error' not in img])
                },
                metadata={
                    'stage': stage,
                    'provider': 'together_ai',
                    'model': self.default_model
                }
            )
            
        except ProviderError:
            raise
        except Exception as e:
            logger.error(f"Together.AI provider error for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def prepare_payload(self, video, stage: str) -> Dict[str, Any]:
        """
        Prepare API payload for Together.AI
        
        Args:
            video: Video model instance
            stage: Stage name
            
        Returns:
            Dict: Together.AI API payload
        """
        # Get image generation parameters
        width, height = self._get_image_dimensions(video.orientation)
        
        payload = {
            'model': self.default_model,
            'width': width,
            'height': height,
            'steps': 20,  # Number of diffusion steps
            'n': 1,       # Number of images to generate per prompt
            'response_format': 'url'  # Return URLs instead of base64
        }
        
        # Add video style to improve prompts
        if video.video_style:
            payload['style'] = video.video_style
        
        return payload
    
    def handle_response(
        self, 
        response: Any, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Process Together.AI API response
        
        Args:
            response: Raw response from Together.AI API
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized response
        """
        try:
            if isinstance(response, dict):
                # Check for API success
                if 'data' in response and response['data']:
                    return ProviderResponse.success(
                        data=response,
                        metadata={'stage': stage, 'provider': 'together_ai'}
                    )
                elif 'error' in response:
                    return ProviderResponse.failure(
                        error_message=response['error'].get('message', 'Together.AI API error'),
                        error_code=response['error'].get('type', 'TOGETHER_AI_ERROR')
                    )
                else:
                    return ProviderResponse.failure(
                        error_message="Unexpected response format from Together.AI",
                        error_code='UNEXPECTED_RESPONSE_FORMAT'
                    )
            else:
                return ProviderResponse.failure(
                    error_message="Invalid response format from Together.AI API",
                    error_code='INVALID_RESPONSE_FORMAT'
                )
                
        except Exception as e:
            logger.error(f"Error handling Together.AI response for stage {stage}: {str(e)}")
            return self.handle_error(e, video, stage)
    
    def handle_error(
        self, 
        error: Exception, 
        video, 
        stage: str
    ) -> ProviderResponse:
        """
        Handle Together.AI provider errors
        
        Args:
            error: Exception that occurred
            video: Video model instance
            stage: Stage name
            
        Returns:
            ProviderResponse: Standardized error response
        """
        error_message = str(error)
        error_code = 'TOGETHER_AI_PROVIDER_ERROR'
        
        # Categorize different types of errors
        if isinstance(error, requests.exceptions.Timeout):
            error_code = 'TOGETHER_AI_TIMEOUT_ERROR'
            error_message = f"Together.AI API timeout for stage {stage}"
        elif isinstance(error, requests.exceptions.ConnectionError):
            error_code = 'TOGETHER_AI_CONNECTION_ERROR'
            error_message = f"Failed to connect to Together.AI API for stage {stage}"
        elif isinstance(error, requests.exceptions.HTTPError):
            error_code = 'TOGETHER_AI_HTTP_ERROR'
            error_message = f"Together.AI API HTTP error for stage {stage}: {error_message}"
        elif isinstance(error, requests.exceptions.RequestException):
            error_code = 'TOGETHER_AI_REQUEST_ERROR'
            error_message = f"Together.AI API request failed for stage {stage}: {error_message}"
        elif isinstance(error, ProviderError):
            error_code = error.error_code or 'TOGETHER_AI_PROVIDER_ERROR'
            error_message = error.message
        
        self.log_operation('handle_error', video.id, stage, {
            'error_code': error_code,
            'error_message': error_message
        })
        
        return ProviderResponse.failure(
            error_message=error_message,
            error_code=error_code,
            metadata={
                'provider': self.provider_name,
                'stage': stage,
                'video_id': video.id
            }
        )
    
    def _make_api_request(self, payload: Dict[str, Any]) -> dict:
        """
        Make HTTP request to Together.AI API
        
        Args:
            payload: Request payload
            
        Returns:
            dict: Response data
            
        Raises:
            requests.RequestException: If request fails
        """
        try:
            headers = {
                'Authorization': f'Bearer {self.api_key}',
                'Content-Type': 'application/json',
                'User-Agent': 'AIVIA-VideoAgent/1.0'
            }
            
            logger.info(f"Making Together.AI API request")
            
            response = requests.post(
                self.API_ENDPOINT,
                json=payload,
                headers=headers,
                timeout=self.timeout
            )
            
            response.raise_for_status()
            
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Together.AI API request failed: {str(e)}")
            raise
    
    def _get_image_prompts(self, video) -> list:
        """
        Get image prompts from video's media generations
        
        Args:
            video: Video model instance
            
        Returns:
            list: List of image prompts
        """
        try:
            prompts = []
            
            # Get image prompt generations for this video
            image_generations = video.media_generations.filter(
                media_type='image_prompt'
            ).order_by('created_at')
            
            for generation in image_generations:
                prompts.append(generation.prompt)
            
            return prompts
            
        except Exception as e:
            logger.error(f"Failed to get image prompts for video {video.id}: {str(e)}")
            return []
    
    def _get_image_dimensions(self, orientation: str) -> tuple:
        """
        Get image dimensions based on video orientation
        
        Args:
            orientation: Video orientation (landscape, portrait, square)
            
        Returns:
            tuple: (width, height)
        """
        if orientation == 'portrait':
            return (512, 768)  # 2:3 aspect ratio
        elif orientation == 'square':
            return (512, 512)  # 1:1 aspect ratio
        else:  # landscape or default
            return (768, 512)  # 3:2 aspect ratio
    
    def _process_image_response(self, response_data: dict, prompt: str, index: int) -> dict:
        """
        Process individual image generation response
        
        Args:
            response_data: API response data
            prompt: Original prompt
            index: Image index
            
        Returns:
            dict: Processed image data
        """
        try:
            if 'data' in response_data and response_data['data']:
                image_data = response_data['data'][0]  # Get first image
                
                return {
                    'url': image_data.get('url'),
                    'prompt': prompt,
                    'index': index,
                    'revised_prompt': image_data.get('revised_prompt', prompt),
                    'metadata': {
                        'provider': 'together_ai',
                        'model': self.default_model
                    }
                }
            else:
                return {
                    'error': 'No image data in response',
                    'prompt': prompt,
                    'index': index
                }
                
        except Exception as e:
            logger.error(f"Failed to process image response: {str(e)}")
            return {
                'error': str(e),
                'prompt': prompt,
                'index': index
            }
    
    def validate_callback_data(self, data: Dict[str, Any], stage: str) -> bool:
        """
        Validate TogetherAI callback data format
        
        Args:
            data: Callback data
            stage: Stage name
            
        Returns:
            bool: True if data is valid
        """
        if stage == 'image_generation':
            return 'generated_images' in data
        return True
    
    def process_callback_data(self, video, stage: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process TogetherAI callback data and update video models
        
        Args:
            video: Video model instance
            stage: Stage name
            data: Callback data from TogetherAI
            
        Returns:
            Dict: Processing result
        """
        try:
            if stage == 'image_generation':
                return self._process_image_generation_callback(video, data)
            else:
                raise ValueError(f"Unsupported stage: {stage}")
                
        except Exception as e:
            self.logger.error(f"Error processing {stage} callback: {e}")
            raise
    
    def _process_image_generation_callback(self, video, data: Dict[str, Any]) -> Dict[str, Any]:
        """Process image generation callback data"""
        from videos.models import MediaAsset, MediaGeneration
        
        # Get existing MediaAssets that need image URLs (empty source_path)
        media_assets = MediaAsset.objects.filter(
            video=video,
            type='image',
            source_path=""  # Only update assets that don't have source paths yet
        ).select_related('generation').order_by('sequence_order')
        
        images = data.get('generated_images', [])
        updated_count = 0
        
        # Update existing MediaAsset entries with generated image URLs
        for index, image_data in enumerate(images):
            if index < media_assets.count():
                media_asset = media_assets[index]
                media_asset.source_path = image_data.get('image_url', '')
                media_asset.duration = image_data.get('duration', 0)
                media_asset.metadata = image_data.get('metadata', {})
                media_asset.save()
                updated_count += 1
                self.logger.info(f"Updated MediaAsset {media_asset.id} with image URL for sequence {media_asset.sequence_order}")
            else:
                # Create new MediaAsset if there are more images than existing assets
                # Find the corresponding MediaGeneration
                generation = None
                if 'generation_id' in image_data:
                    try:
                        generation = MediaGeneration.objects.get(
                            id=image_data['generation_id'],
                            video=video
                        )
                    except MediaGeneration.DoesNotExist:
                        pass
                
                MediaAsset.objects.create(
                    video=video,
                    generation=generation,
                    source_path=image_data.get('image_url', ''),
                    type='image',
                    duration=image_data.get('duration', 0),
                    metadata=image_data.get('metadata', {}),
                    sequence_order=image_data.get('sequence_order', index + 1),
                )
                updated_count += 1
                self.logger.info(f"Created additional MediaAsset for sequence {index + 1}")
        
        self.logger.info(f"Updated/created {updated_count} image assets for video {video.id}")
        
        return {
            'assets_updated': updated_count,
            'message': f'Updated/created {updated_count} image assets'
        }

    def _create_media_assets(self, video, generated_images: list):
        """
        Create MediaAsset records for generated images
        
        Args:
            video: Video model instance
            generated_images: List of generated image data
        """
        try:
            from ...models import MediaGeneration, MediaAsset
            
            # Get or create image generation record
            generation, created = MediaGeneration.objects.get_or_create(
                video=video,
                media_type='image',
                media_provider='together_ai',
                defaults={
                    'prompt': f"Generated {len(generated_images)} images for video"
                }
            )
            
            # Create MediaAsset for each successful image
            for image_data in generated_images:
                if 'error' not in image_data and image_data.get('url'):
                    MediaAsset.objects.create(
                        generation=generation,
                        source_path=image_data['url'],
                        type='image',
                        metadata={
                            'prompt': image_data.get('prompt', ''),
                            'revised_prompt': image_data.get('revised_prompt', ''),
                            'index': image_data.get('index', 0),
                            'provider': 'together_ai',
                            'model': self.default_model
                        }
                    )
            
            logger.info(f"Created MediaAssets for {len([img for img in generated_images if 'error' not in img])} images")
            
        except Exception as e:
            logger.error(f"Failed to create MediaAssets: {str(e)}")
